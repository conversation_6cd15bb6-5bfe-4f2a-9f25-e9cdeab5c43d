#include <Windows.h>
#include <cstdio>

// 只包含必要的部分进行测试
#include "wnbios_poc/drv.h"

int main()
{
    printf("[+] Starting minimal test...\n");
    
    try
    {
        eneio_lib driver;
        
        // 只测试最基本的功能
        printf("[+] Driver initialized successfully\n");
        
        // 测试获取进程基址
        uintptr_t base = driver.get_process_base("explorer.exe");
        if (base)
        {
            printf("[+] Explorer.exe base: 0x%llx\n", base);
            
            // 测试基本的内存读取
            UINT8 buffer[4] = {0};
            bool result = driver.read_virtual_memory(base, buffer, 2);
            if (result)
            {
                printf("[+] Memory read successful: %02X %02X\n", buffer[0], buffer[1]);
            }
            else
            {
                printf("[-] Memory read failed\n");
            }
        }
        else
        {
            printf("[-] Failed to get explorer.exe base\n");
        }
        
        printf("[+] Test completed\n");
    }
    catch (...)
    {
        printf("[-] Exception occurred\n");
        return 1;
    }
    
    printf("Press any key to continue...\n");
    system("pause");
    return 0;
}
