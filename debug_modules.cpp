#include <Windows.h>
#include <cstdio>

#include "wnbios_poc/drv.h"

int main()
{
    printf("[+] Debug Module Enumeration Test\n");
    printf("=====================================\n");
    
    try
    {
        eneio_lib driver;
        printf("[+] Driver initialized\n");
        
        // 首先测试基本功能
        uintptr_t base = driver.get_process_base("explorer.exe");
        if (!base)
        {
            printf("[-] Failed to get explorer.exe base address\n");
            printf("[-] Make sure explorer.exe is running\n");
            system("pause");
            return 1;
        }
        
        printf("[+] Explorer.exe base: 0x%llx\n", base);
        
        // 测试基本内存读取
        UINT8 buffer[4] = {0};
        if (driver.read_virtual_memory(base, buffer, 2))
        {
            printf("[+] Memory read test: %02X %02X (should be 4D 5A)\n", buffer[0], buffer[1]);
        }
        else
        {
            printf("[-] Memory read test failed\n");
        }
        
        printf("\n[*] Starting module enumeration debug...\n");
        printf("==========================================\n");
        
        // 尝试模块枚举
        std::vector<MODULE_INFO> modules = driver.enumerate_process_modules("explorer.exe");
        
        printf("\n[+] Module enumeration completed!\n");
        printf("[+] Total modules found: %d\n", (int)modules.size());
        
        if (!modules.empty())
        {
            printf("\n[+] First few modules:\n");
            int count = 0;
            for (const auto& module : modules)
            {
                if (!module.name.empty())
                {
                    printf("  %d. %s\n", count + 1, module.name.c_str());
                    printf("     Base: 0x%llx, Size: 0x%x\n", 
                           module.base_address, (unsigned int)module.size);
                    
                    count++;
                    if (count >= 5) // 只显示前5个
                    {
                        printf("  ... (showing first 5 modules only)\n");
                        break;
                    }
                }
            }
        }
        else
        {
            printf("[-] No modules found - check debug output above\n");
        }
        
        printf("\n[+] Test completed\n");
    }
    catch (...)
    {
        printf("[-] Exception occurred during test\n");
        return 1;
    }
    
    printf("\nPress any key to continue...\n");
    system("pause");
    return 0;
}
