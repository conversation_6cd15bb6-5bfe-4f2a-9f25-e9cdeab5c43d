﻿  drv.cpp
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1226,16): error C2011: “_UNICODE_STRING”:“struct”类型重定义
  (编译源文件“drv.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winternl.h(70,16):
      参见“_UNICODE_STRING”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1232,16): error C2011: “_LIST_ENTRY”:“struct”类型重定义
  (编译源文件“drv.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(1224,16):
      参见“_LIST_ENTRY”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1238,16): error C2011: “_PEB_LDR_DATA”:“struct”类型重定义
  (编译源文件“drv.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winternl.h(140,16):
      参见“_PEB_LDR_DATA”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1248,16): error C2011: “_LDR_DATA_TABLE_ENTRY”:“struct”类型重定义
  (编译源文件“drv.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winternl.h(146,16):
      参见“_LDR_DATA_TABLE_ENTRY”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(594,12): warning C4244: “初始化”: 从“uintptr_t”转换到“DWORD”，可能丢失数据
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(617,17): error C2079: “unicode_str”使用未定义的 struct“_UNICODE_STRING”
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(661,15): error C2079: “ldr_data”使用未定义的 struct“_PEB_LDR_DATA”
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(674,24): error C2079: “entry”使用未定义的 struct“_LDR_DATA_TABLE_ENTRY”
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(705,12): warning C4244: “初始化”: 从“uintptr_t”转换到“DWORD”，可能丢失数据
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(741,12): warning C4244: “初始化”: 从“uintptr_t”转换到“DWORD”，可能丢失数据
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(773,12): warning C4244: “初始化”: 从“uintptr_t”转换到“DWORD”，可能丢失数据
  wnbios_poc.cpp
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1226,16): error C2011: “_UNICODE_STRING”:“struct”类型重定义
  (编译源文件“wnbios_poc.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winternl.h(70,16):
      参见“_UNICODE_STRING”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1232,16): error C2011: “_LIST_ENTRY”:“struct”类型重定义
  (编译源文件“wnbios_poc.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(1224,16):
      参见“_LIST_ENTRY”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1238,16): error C2011: “_PEB_LDR_DATA”:“struct”类型重定义
  (编译源文件“wnbios_poc.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winternl.h(140,16):
      参见“_PEB_LDR_DATA”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1248,16): error C2011: “_LDR_DATA_TABLE_ENTRY”:“struct”类型重定义
  (编译源文件“wnbios_poc.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winternl.h(146,16):
      参见“_LDR_DATA_TABLE_ENTRY”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\wnbios_poc.cpp(26,15): error C2248: “eneio_lib::read_virtual_memory”: 无法访问 private 成员(在“eneio_lib”类中声明)
      C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1359,7):
      参见“eneio_lib::read_virtual_memory”的声明
      C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1282,7):
      参见“eneio_lib”的声明
  
  正在生成代码...
