﻿  drv.cpp
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(598,12): warning C4244: “初始化”: 从“uintptr_t”转换到“DWORD”，可能丢失数据
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(711,12): warning C4244: “初始化”: 从“uintptr_t”转换到“DWORD”，可能丢失数据
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(748,12): warning C4244: “初始化”: 从“uintptr_t”转换到“DWORD”，可能丢失数据
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(780,12): warning C4244: “初始化”: 从“uintptr_t”转换到“DWORD”，可能丢失数据
  wnbios_poc.cpp
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\wnbios_poc.cpp(27,15): error C2248: “eneio_lib::read_virtual_memory”: 无法访问 private 成员(在“eneio_lib”类中声明)
      C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1365,7):
      参见“eneio_lib::read_virtual_memory”的声明
      C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1288,7):
      参见“eneio_lib”的声明
  
  正在生成代码...
