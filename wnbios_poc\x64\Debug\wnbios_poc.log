﻿  drv.cpp
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1225,8): error C2371: “PEB_LDR_DATA”: 重定义；不同的基类型
  (编译源文件“drv.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winternl.h(144,3):
      参见“PEB_LDR_DATA”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1235,8): error C2371: “LDR_DATA_TABLE_ENTRY”: 重定义；不同的基类型
  (编译源文件“drv.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winternl.h(163,3):
      参见“LDR_DATA_TABLE_ENTRY”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(586,12): warning C4244: “初始化”: 从“uintptr_t”转换到“DWORD”，可能丢失数据
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(658,65): error C2039: "InLoadOrderModuleList": 不是 "_PEB_LDR_DATA" 的成员
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winternl.h(140,16):
      参见“_PEB_LDR_DATA”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(659,39): error C2039: "InLoadOrderModuleList": 不是 "_PEB_LDR_DATA" 的成员
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winternl.h(140,16):
      参见“_PEB_LDR_DATA”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(659,39): error C2618: offsetof 中的非法成员指示符
      C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(659,39):
      offsetof 具有内在含义；使用 /Zc:offsetof- 还原到旧的非符合定义
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(672,28): error C2039: "SizeOfImage": 不是 "_LDR_DATA_TABLE_ENTRY" 的成员
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winternl.h(146,16):
      参见“_LDR_DATA_TABLE_ENTRY”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(675,46): error C2039: "BaseDllName": 不是 "_LDR_DATA_TABLE_ENTRY" 的成员
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winternl.h(146,16):
      参见“_LDR_DATA_TABLE_ENTRY”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(675,46): error C2618: offsetof 中的非法成员指示符
      C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(675,46):
      offsetof 具有内在含义；使用 /Zc:offsetof- 还原到旧的非符合定义
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(687,53): error C2039: "InLoadOrderLinks": 不是 "_LDR_DATA_TABLE_ENTRY" 的成员
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winternl.h(146,16):
      参见“_LDR_DATA_TABLE_ENTRY”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(697,12): warning C4244: “初始化”: 从“uintptr_t”转换到“DWORD”，可能丢失数据
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(733,12): warning C4244: “初始化”: 从“uintptr_t”转换到“DWORD”，可能丢失数据
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.cpp(765,12): warning C4244: “初始化”: 从“uintptr_t”转换到“DWORD”，可能丢失数据
  wnbios_poc.cpp
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1225,8): error C2371: “PEB_LDR_DATA”: 重定义；不同的基类型
  (编译源文件“wnbios_poc.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winternl.h(144,3):
      参见“PEB_LDR_DATA”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1235,8): error C2371: “LDR_DATA_TABLE_ENTRY”: 重定义；不同的基类型
  (编译源文件“wnbios_poc.cpp”)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winternl.h(163,3):
      参见“LDR_DATA_TABLE_ENTRY”的声明
  
C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\wnbios_poc.cpp(90,11): error C2248: “eneio_lib::read_virtual_memory”: 无法访问 private 成员(在“eneio_lib”类中声明)
      C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1346,7):
      参见“eneio_lib::read_virtual_memory”的声明
      C:\Users\<USER>\Desktop\驱动\test1\wnbios_poc\drv.h(1269,7):
      参见“eneio_lib”的声明
  
  正在生成代码...
