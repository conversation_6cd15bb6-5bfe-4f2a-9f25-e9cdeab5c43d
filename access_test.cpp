#include <Windows.h>
#include <cstdio>

#include "wnbios_poc/drv.h"

int main()
{
    printf("[+] Testing function access...\n");
    
    try
    {
        eneio_lib driver;
        printf("[+] Driver object created successfully\n");
        
        // 测试公共函数访问
        printf("[+] Testing public function access:\n");
        
        // 1. 测试 get_process_base (应该可以访问)
        uintptr_t base = driver.get_process_base("explorer.exe");
        printf("  - get_process_base: %s\n", base ? "OK" : "Failed");
        
        if (base)
        {
            // 2. 测试 read_virtual_memory (现在应该可以访问)
            UINT8 buffer[4] = {0};
            bool result = driver.read_virtual_memory(base, buffer, 2);
            printf("  - read_virtual_memory: %s\n", result ? "OK" : "Failed");
            
            if (result)
            {
                printf("    Read data: %02X %02X\n", buffer[0], buffer[1]);
            }
        }
        
        // 3. 测试模块枚举函数
        std::vector<MODULE_INFO> modules = driver.enumerate_process_modules("explorer.exe");
        printf("  - enumerate_process_modules: %s (%d modules)\n", 
               modules.empty() ? "Failed" : "OK", (int)modules.size());
        
        // 4. 测试模块查找函数
        uintptr_t ntdll_base = driver.get_module_base("explorer.exe", "ntdll.dll");
        printf("  - get_module_base: %s\n", ntdll_base ? "OK" : "Failed");
        
        printf("\n[+] All function access tests completed!\n");
    }
    catch (...)
    {
        printf("[-] Exception occurred during test\n");
        return 1;
    }
    
    printf("\nPress any key to continue...\n");
    system("pause");
    return 0;
}
