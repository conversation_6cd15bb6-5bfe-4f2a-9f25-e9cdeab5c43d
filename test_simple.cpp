#include <Windows.h>
#include <cstdio>

#include "drv.h"

int main()
{
    printf("[+] Starting simple module enumeration test...\n");
    
    try
    {
        eneio_lib driver;
        
        // 测试原始功能
        printf("\n=== Original Memory Read Test ===\n");
        uintptr_t base = driver.get_process_base("explorer.exe");
        if (base)
        {
            printf("[+] Explorer.exe base address: 0x%llx\n", base);
            
            UINT8 buf[3] = { 0 };
            if (driver.read_virtual_memory(base, buf, 2))
            {
                printf("[+] Read first 2 bytes: %02X %02X\n", buf[0], buf[1]);
            }
        }
        else
        {
            printf("[-] Failed to get explorer.exe base address\n");
        }
        
        // 测试特定模块查找
        printf("\n=== Specific Module Test ===\n");
        uintptr_t ntdll_base = driver.get_module_base("explorer.exe", "ntdll.dll");
        if (ntdll_base)
        {
            printf("[+] ntdll.dll base address: 0x%llx\n", ntdll_base);
        }
        else
        {
            printf("[-] ntdll.dll not found\n");
        }
        
        uintptr_t kernel32_base = driver.get_module_base("explorer.exe", "kernel32.dll");
        if (kernel32_base)
        {
            printf("[+] kernel32.dll base address: 0x%llx\n", kernel32_base);
        }
        else
        {
            printf("[-] kernel32.dll not found\n");
        }
        
        printf("\n[+] Test completed successfully!\n");
    }
    catch (...)
    {
        printf("[-] Exception occurred during test\n");
    }
    
    printf("\nPress any key to continue...\n");
    system("pause");

    return 0;
}
