#include <Windows.h>
#include <cstdio>
#include <vector>

#include "wnbios_poc/drv.h"

int main()
{
    printf("[+] WnBios POC - Module Enumeration Tool\n");
    printf("========================================\n");
    
    try
    {
        eneio_lib driver;
        printf("[+] Driver initialized successfully\n");
        
        // 测试基础功能
        printf("\n=== Basic Memory Test ===\n");
        uintptr_t base = driver.get_process_base("explorer.exe");
        if (base)
        {
            printf("[+] Explorer.exe base address: 0x%llx\n", base);
            
            UINT8 buf[4] = {0};
            if (driver.read_virtual_memory(base, buf, 2))
            {
                printf("[+] PE header: %02X %02X %s\n", 
                       buf[0], buf[1], 
                       (buf[0] == 0x4D && buf[1] == 0x5A) ? "(Valid PE)" : "(Invalid)");
            }
        }
        else
        {
            printf("[-] Failed to get explorer.exe base address\n");
            return 1;
        }
        
        // 测试模块枚举
        printf("\n=== Module Enumeration ===\n");
        printf("[*] Enumerating modules for explorer.exe...\n");
        
        std::vector<MODULE_INFO> modules = driver.enumerate_process_modules("explorer.exe");
        
        printf("[+] Found %d modules:\n", (int)modules.size());
        
        if (!modules.empty())
        {
            printf("\n%-4s %-25s %-18s %-10s\n", "No.", "Module Name", "Base Address", "Size");
            printf("%-4s %-25s %-18s %-10s\n", "---", "-------------------------", "------------------", "----------");
            
            int count = 0;
            for (const auto& module : modules)
            {
                if (!module.name.empty() && module.base_address != 0)
                {
                    printf("%-4d %-25s 0x%-16llx 0x%-8x\n", 
                           count + 1,
                           module.name.c_str(),
                           module.base_address,
                           (unsigned int)module.size);
                    
                    count++;
                    if (count >= 20) // 显示前20个模块
                    {
                        printf("     ... (showing first 20 modules, total: %d)\n", (int)modules.size());
                        break;
                    }
                }
            }
        }
        
        // 测试特定模块查找
        printf("\n=== Specific Module Tests ===\n");
        
        struct TestModule {
            const char* name;
            const char* description;
        };
        
        TestModule test_modules[] = {
            {"ntdll.dll", "NT Layer DLL"},
            {"kernel32.dll", "Win32 Base API"},
            {"kernelbase.dll", "Kernel Base"},
            {"user32.dll", "User Interface"},
            {"shell32.dll", "Shell Common Dll"}
        };
        
        for (const auto& test_mod : test_modules)
        {
            uintptr_t mod_base = driver.get_module_base("explorer.exe", test_mod.name);
            if (mod_base)
            {
                printf("[+] %-15s: 0x%llx (%s)\n", test_mod.name, mod_base, test_mod.description);
            }
            else
            {
                printf("[-] %-15s: Not found\n", test_mod.name);
            }
        }
        
        printf("\n[+] All tests completed successfully!\n");
        printf("[+] Module enumeration functionality is working correctly.\n");
    }
    catch (...)
    {
        printf("[-] Exception occurred during execution\n");
        return 1;
    }
    
    printf("\nPress any key to exit...\n");
    system("pause");
    return 0;
}
