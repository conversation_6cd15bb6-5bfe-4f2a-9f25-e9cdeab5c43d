#include <Windows.h>
#include <iostream>
#include <iomanip>
#include <vector>

#include "drv.h"

void print_module_info(const MODULE_INFO& module)
{
	std::cout << "  Module: " << module.name << std::endl;
	std::cout << "    Base: 0x" << std::hex << std::uppercase << module.base_address << std::endl;
	std::cout << "    Size: 0x" << std::hex << std::uppercase << module.size << std::endl;
	std::cout << "    Path: " << module.full_path << std::endl;
	std::cout << std::endl;
}

void test_module_enumeration(eneio_lib& driver, const char* process_name)
{
	std::cout << "=== Testing Module Enumeration for " << process_name << " ===" << std::endl;

	// 获取进程基址
	uintptr_t base = driver.get_process_base(process_name);
	if (!base)
	{
		std::cout << "[-] Process " << process_name << " is not running" << std::endl;
		return;
	}

	std::cout << "[+] Process " << process_name << " base address: 0x"
		<< std::hex << std::uppercase << base << std::endl << std::endl;

	// 枚举所有模块
	std::vector<MODULE_INFO> modules = driver.enumerate_process_modules(process_name);
	std::cout << "[+] Found " << std::dec << modules.size() << " modules:" << std::endl;

	for (const auto& module : modules)
	{
		print_module_info(module);
	}
}

void test_specific_module(eneio_lib& driver, const char* process_name, const char* module_name)
{
	std::cout << "=== Testing Specific Module: " << module_name << " in " << process_name << " ===" << std::endl;

	// 获取特定模块的基址
	uintptr_t module_base = driver.get_module_base(process_name, module_name);
	if (module_base)
	{
		std::cout << "[+] " << module_name << " base address: 0x"
			<< std::hex << std::uppercase << module_base << std::endl;

		// 获取模块详细信息
		MODULE_INFO module_info = driver.get_module_info(process_name, module_name);
		if (!module_info.name.empty())
		{
			print_module_info(module_info);
		}
	}
	else
	{
		std::cout << "[-] Module " << module_name << " not found in " << process_name << std::endl;
	}
}

int main()
{
	printf("[+] Starting wnbios_poc with module enumeration...\n");

	try
	{
		eneio_lib driver;

		// 测试原始功能
		printf("\n=== Original Memory Read Test ===\n");
		uintptr_t base = driver.get_process_base("explorer.exe");
		if (base)
		{
			printf("[+] Explorer.exe base address: 0x%llx\n", base);

			UINT8 buf[3] = { 0 };
			if (driver.read_virtual_memory(base, buf, 2))
			{
				printf("[+] Read first 2 bytes: %02X %02X\n", buf[0], buf[1]);
			}
		}
		else
		{
			printf("[-] Failed to get explorer.exe base address\n");
		}

		// 测试模块枚举
		printf("\n=== Module Enumeration Test ===\n");
		std::vector<MODULE_INFO> modules = driver.enumerate_process_modules("explorer.exe");
		printf("[+] Found %d modules in explorer.exe:\n", (int)modules.size());

		int count = 0;
		for (const auto& module : modules)
		{
			printf("  [%d] %s - Base: 0x%llx, Size: 0x%x\n",
				count++, module.name.c_str(), module.base_address, (unsigned int)module.size);

			if (count >= 10) // 只显示前10个模块
			{
				printf("  ... (showing first 10 modules only)\n");
				break;
			}
		}

		// 测试特定模块查找
		printf("\n=== Specific Module Test ===\n");
		uintptr_t ntdll_base = driver.get_module_base("explorer.exe", "ntdll.dll");
		if (ntdll_base)
		{
			printf("[+] ntdll.dll base address: 0x%llx\n", ntdll_base);
		}
		else
		{
			printf("[-] ntdll.dll not found\n");
		}

		uintptr_t kernel32_base = driver.get_module_base("explorer.exe", "kernel32.dll");
		if (kernel32_base)
		{
			printf("[+] kernel32.dll base address: 0x%llx\n", kernel32_base);
		}
		else
		{
			printf("[-] kernel32.dll not found\n");
		}
	}
	catch (...)
	{
		printf("[-] Exception occurred\n");
	}

	printf("\nPress any key to continue...\n");
	system("pause");

	return 0;
}