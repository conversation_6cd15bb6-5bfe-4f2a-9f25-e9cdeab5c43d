#include <Windows.h>
#include <iostream>
#include <iomanip>
#include <vector>
#include <cstdio>

#include "drv.h"



int main()
{
	printf("[+] Starting wnbios_poc with module enumeration...\n");

	try
	{
		eneio_lib driver;

		// 测试原始功能
		printf("\n=== Original Memory Read Test ===\n");
		uintptr_t base = driver.get_process_base("explorer.exe");
		if (base)
		{
			printf("[+] Explorer.exe base address: 0x%llx\n", base);

			UINT8 buf[3] = { 0 };
			if (driver.read_virtual_memory(base, buf, 2))
			{
				printf("[+] Read first 2 bytes: %02X %02X\n", buf[0], buf[1]);
			}
		}
		else
		{
			printf("[-] Failed to get explorer.exe base address\n");
		}

		// 测试模块枚举
		printf("\n=== Module Enumeration Test ===\n");
		std::vector<MODULE_INFO> modules = driver.enumerate_process_modules("explorer.exe");
		printf("[+] Found %d modules in explorer.exe:\n", (int)modules.size());

		int count = 0;
		for (const auto& module : modules)
		{
			printf("  [%d] %s - Base: 0x%llx, Size: 0x%x\n",
				count++, module.name.c_str(), module.base_address, (unsigned int)module.size);

			if (count >= 10) // 只显示前10个模块
			{
				printf("  ... (showing first 10 modules only)\n");
				break;
			}
		}

		// 测试特定模块查找
		printf("\n=== Specific Module Test ===\n");
		uintptr_t ntdll_base = driver.get_module_base("explorer.exe", "ntdll.dll");
		if (ntdll_base)
		{
			printf("[+] ntdll.dll base address: 0x%llx\n", ntdll_base);
		}
		else
		{
			printf("[-] ntdll.dll not found\n");
		}

		uintptr_t kernel32_base = driver.get_module_base("explorer.exe", "kernel32.dll");
		if (kernel32_base)
		{
			printf("[+] kernel32.dll base address: 0x%llx\n", kernel32_base);
		}
		else
		{
			printf("[-] kernel32.dll not found\n");
		}
	}
	catch (...)
	{
		printf("[-] Exception occurred\n");
	}

	printf("\nPress any key to continue...\n");
	system("pause");

	return 0;
}