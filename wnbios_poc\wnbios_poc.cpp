#include <Windows.h>
#include <map>
#include <iostream>
#include <iomanip>

#include "drv.h"

void print_module_info(const MODULE_INFO& module)
{
	std::cout << "  Module: " << module.name << std::endl;
	std::cout << "    Base: 0x" << std::hex << std::uppercase << module.base_address << std::endl;
	std::cout << "    Size: 0x" << std::hex << std::uppercase << module.size << std::endl;
	std::cout << "    Path: " << module.full_path << std::endl;
	std::cout << std::endl;
}

void test_module_enumeration(eneio_lib& driver, const char* process_name)
{
	std::cout << "=== Testing Module Enumeration for " << process_name << " ===" << std::endl;

	// 获取进程基址
	uintptr_t base = driver.get_process_base(process_name);
	if (!base)
	{
		std::cout << "[-] Process " << process_name << " is not running" << std::endl;
		return;
	}

	std::cout << "[+] Process " << process_name << " base address: 0x"
		<< std::hex << std::uppercase << base << std::endl << std::endl;

	// 枚举所有模块
	std::vector<MODULE_INFO> modules = driver.enumerate_process_modules(process_name);
	std::cout << "[+] Found " << std::dec << modules.size() << " modules:" << std::endl;

	for (const auto& module : modules)
	{
		print_module_info(module);
	}
}

void test_specific_module(eneio_lib& driver, const char* process_name, const char* module_name)
{
	std::cout << "=== Testing Specific Module: " << module_name << " in " << process_name << " ===" << std::endl;

	// 获取特定模块的基址
	uintptr_t module_base = driver.get_module_base(process_name, module_name);
	if (module_base)
	{
		std::cout << "[+] " << module_name << " base address: 0x"
			<< std::hex << std::uppercase << module_base << std::endl;

		// 获取模块详细信息
		MODULE_INFO module_info = driver.get_module_info(process_name, module_name);
		if (!module_info.name.empty())
		{
			print_module_info(module_info);
		}
	}
	else
	{
		std::cout << "[-] Module " << module_name << " not found in " << process_name << std::endl;
	}
}

int main()
{
	try
	{
		eneio_lib driver;

		// 测试进程模块枚举
		test_module_enumeration(driver, "explorer.exe");

		std::cout << std::string(60, '=') << std::endl;

		// 测试特定模块查找
		test_specific_module(driver, "explorer.exe", "ntdll.dll");
		test_specific_module(driver, "explorer.exe", "kernel32.dll");
		test_specific_module(driver, "explorer.exe", "user32.dll");

		std::cout << std::string(60, '=') << std::endl;

		// 原始功能测试
		std::cout << "=== Original Memory Read Test ===" << std::endl;
		uintptr_t base = driver.get_process_base("explorer.exe");
		if (base)
		{
			UINT8 buf[3] = { 0 };
			driver.read_virtual_memory(base, buf, 2);
			std::cout << "[+] Read first 2 bytes from explorer.exe: ";
			for (int i = 0; i < 2; i++)
			{
				std::cout << std::hex << std::setw(2) << std::setfill('0') << (int)buf[i] << " ";
			}
			std::cout << std::endl;
		}
	}
	catch (const std::exception& e)
	{
		std::cout << "[-] Exception: " << e.what() << std::endl;
	}

	std::cout << std::endl << "Press any key to continue...";
	system("pause");

	return 0;
}