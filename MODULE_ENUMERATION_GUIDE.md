# 进程模块枚举功能使用指南

## 概述

本项目已扩展了原有的内存读写功能，新增了完整的进程模块枚举和查找功能。这些功能允许您：

1. 枚举指定进程的所有加载模块
2. 查找特定模块的基地址
3. 获取模块的详细信息（名称、大小、路径等）

## 新增功能

### 1. 模块信息结构体

```cpp
struct MODULE_INFO
{
    std::string name;           // 模块名称 (例如: "ntdll.dll")
    uintptr_t base_address;     // 模块基地址
    size_t size;                // 模块大小
    std::string full_path;      // 模块完整路径
};
```

### 2. 主要函数

#### 枚举进程模块
```cpp
// 通过进程名枚举模块
std::vector<MODULE_INFO> enumerate_process_modules(const char* process_name);

// 通过进程ID枚举模块
std::vector<MODULE_INFO> enumerate_process_modules_by_pid(DWORD process_id);
```

#### 获取特定模块基址
```cpp
// 通过进程名和模块名获取模块基址
uintptr_t get_module_base(const char* process_name, const char* module_name);

// 通过进程ID和模块名获取模块基址
uintptr_t get_module_base_by_pid(DWORD process_id, const char* module_name);
```

#### 获取模块详细信息
```cpp
// 通过进程名和模块名获取模块详细信息
MODULE_INFO get_module_info(const char* process_name, const char* module_name);

// 通过进程ID和模块名获取模块详细信息
MODULE_INFO get_module_info_by_pid(DWORD process_id, const char* module_name);
```

## 使用示例

### 示例1：枚举进程的所有模块

```cpp
eneio_lib driver;

// 枚举 explorer.exe 的所有模块
std::vector<MODULE_INFO> modules = driver.enumerate_process_modules("explorer.exe");

std::cout << "Found " << modules.size() << " modules:" << std::endl;
for (const auto& module : modules)
{
    std::cout << "Module: " << module.name 
              << " Base: 0x" << std::hex << module.base_address
              << " Size: 0x" << std::hex << module.size << std::endl;
}
```

### 示例2：查找特定模块

```cpp
eneio_lib driver;

// 获取 ntdll.dll 在 explorer.exe 中的基址
uintptr_t ntdll_base = driver.get_module_base("explorer.exe", "ntdll.dll");

if (ntdll_base)
{
    std::cout << "ntdll.dll base address: 0x" << std::hex << ntdll_base << std::endl;
    
    // 获取详细信息
    MODULE_INFO ntdll_info = driver.get_module_info("explorer.exe", "ntdll.dll");
    std::cout << "Full path: " << ntdll_info.full_path << std::endl;
    std::cout << "Size: 0x" << std::hex << ntdll_info.size << std::endl;
}
```

### 示例3：结合内存读写功能

```cpp
eneio_lib driver;

// 获取目标进程的主模块基址
uintptr_t main_module = driver.get_process_base("target.exe");

// 获取 kernel32.dll 基址
uintptr_t kernel32_base = driver.get_module_base("target.exe", "kernel32.dll");

if (kernel32_base)
{
    // 读取 kernel32.dll 的 PE 头
    IMAGE_DOS_HEADER dos_header;
    driver.read_virtual_memory(kernel32_base, &dos_header, sizeof(dos_header));
    
    std::cout << "DOS signature: " << std::hex << dos_header.e_magic << std::endl;
}
```

## 技术实现细节

### PEB (Process Environment Block) 解析
新功能通过解析目标进程的 PEB 结构来获取模块信息：

1. 通过 EPROCESS 结构获取进程的 PEB 地址
2. 从 PEB 中读取 PEB_LDR_DATA 结构
3. 遍历 InLoadOrderModuleList 链表
4. 解析每个 LDR_DATA_TABLE_ENTRY 获取模块信息

### 地址空间切换
为了正确读取目标进程的内存，系统会自动切换到目标进程的地址空间（CR3）。

### Unicode 字符串处理
模块名称和路径以 Unicode 格式存储，系统会自动转换为 UTF-8 字符串。

## 注意事项

1. **权限要求**: 需要管理员权限运行
2. **进程状态**: 目标进程必须正在运行
3. **模块名称**: 模块名称不区分大小写
4. **内存保护**: 某些系统进程可能受到额外保护
5. **稳定性**: 建议在测试环境中使用

## 错误处理

- 如果进程不存在，相关函数会返回空值或空向量
- 如果模块不存在，会返回 0 或空的 MODULE_INFO 结构
- 内存读取失败时会返回相应的错误状态

## 兼容性

支持的 Windows 版本：
- Windows 10 (所有版本)
- Windows 11

支持的架构：
- x64 (64位)
